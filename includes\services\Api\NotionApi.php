<?php
declare(strict_types=1);

namespace NTWP\Services\Api;

use NTWP\Core\Logger;
use NTWP\Infrastructure\Concurrency\ConcurrencyManager;
use NTWP\Infrastructure\Cache\CacheManager;
use NTWP\Utils\NetworkRetry;
use NTWP\Services\Api\ApiInterface;
use NTWP\Utils\ApiResult;

/**
 * Notion API 统一服务
 *
 * 合并原有的API.php (2923行) 和 ApiService.php (310行)
 * 提供统一的Notion API交互功能
 *
 * @since      2.0.0-beta.1
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 */
class NotionApi implements ApiInterface {
    
    private string $api_token;
    private string $base_url = 'https://api.notion.com/v1';
    private ConcurrencyManager $concurrency;
    private CacheManager $cache;
    private NetworkRetry $retry;
    
    /**
     * API版本
     */
    private const API_VERSION = '2022-06-28';
    
    /**
     * 请求头模板
     */
    private array $default_headers;
    
    public function __construct(
        string $api_token,
        ConcurrencyManager $concurrency,
        CacheManager $cache,
        NetworkRetry $retry
    ) {
        $this->api_token = $api_token;
        $this->concurrency = $concurrency;
        $this->cache = $cache;
        $this->retry = $retry;
        
        $this->default_headers = [
            'Authorization' => 'Bearer ' . $this->api_token,
            'Notion-Version' => self::API_VERSION,
            'Content-Type' => 'application/json',
            'User-Agent' => 'Notion-to-WordPress/2.0.0'
        ];
    }
    
    /**
     * 获取数据库页面
     *
     * @param string $database_id 数据库ID
     * @param array $options 查询选项
     * @return array 页面数据
     */
    public function fetch_database_pages(string $database_id, array $options = []): array {
        $cache_key = "db_pages_{$database_id}_" . md5(serialize($options));
        
        // 检查缓存
        $cached = $this->cache->get($cache_key, 'api_response');
        if ($cached !== null) {
            Logger::debug_log('使用缓存的数据库页面', 'NotionApi');
            return $cached;
        }
        
        $all_pages = [];
        $next_cursor = null;
        
        do {
            $request_options = array_merge($options, [
                'start_cursor' => $next_cursor
            ]);
            
            $response = $this->make_request(
                'POST',
                "/databases/{$database_id}/query",
                $request_options
            );
            
            if (!$response || !isset($response['results'])) {
                break;
            }
            
            $all_pages = array_merge($all_pages, $response['results']);
            $next_cursor = $response['next_cursor'] ?? null;
            
        } while ($next_cursor);
        
        // 缓存结果
        $this->cache->set($cache_key, $all_pages, 300, 'api_response');
        
        Logger::debug_log(
            sprintf('获取数据库页面: %d 页', count($all_pages)),
            'NotionApi'
        );
        
        return $all_pages;
    }
    
    /**
     * 获取页面内容
     *
     * @param string $page_id 页面ID
     * @return array|null 页面数据
     */
    public function fetch_page(string $page_id): ?array {
        $cache_key = "page_{$page_id}";
        
        // 检查缓存
        $cached = $this->cache->get($cache_key, 'api_response');
        if ($cached !== null) {
            return $cached;
        }
        
        $response = $this->make_request('GET', "/pages/{$page_id}");
        
        if ($response) {
            $this->cache->set($cache_key, $response, 300, 'api_response');
        }
        
        return $response;
    }
    
    /**
     * 获取页面块内容
     *
     * @param string $page_id 页面ID
     * @return array 块数据
     */
    public function fetch_page_blocks(string $page_id): array {
        $cache_key = "blocks_{$page_id}";
        
        // 检查缓存
        $cached = $this->cache->get($cache_key, 'api_response');
        if ($cached !== null) {
            return $cached;
        }
        
        $all_blocks = [];
        $next_cursor = null;
        
        do {
            $url = "/blocks/{$page_id}/children";
            if ($next_cursor) {
                $url .= "?start_cursor={$next_cursor}";
            }
            
            $response = $this->make_request('GET', $url);
            
            if (!$response || !isset($response['results'])) {
                break;
            }
            
            $all_blocks = array_merge($all_blocks, $response['results']);
            $next_cursor = $response['next_cursor'] ?? null;
            
        } while ($next_cursor);
        
        // 缓存结果
        $this->cache->set($cache_key, $all_blocks, 300, 'api_response');
        
        return $all_blocks;
    }
    
    /**
     * 批量获取多个页面
     *
     * @param array $page_ids 页面ID数组
     * @return array 页面数据映射
     */
    public function batch_fetch_pages(array $page_ids): array {
        if (empty($page_ids)) {
            return [];
        }
        
        // 使用并发管理器进行批量请求
        $requests = [];
        foreach ($page_ids as $page_id) {
            $requests[$page_id] = [
                'method' => 'GET',
                'url' => $this->base_url . "/pages/{$page_id}",
                'headers' => $this->default_headers
            ];
        }
        
        $responses = $this->concurrency->execute_concurrent_requests($requests);
        
        $pages = [];
        foreach ($responses as $page_id => $response) {
            if ($response && isset($response['body'])) {
                $pages[$page_id] = json_decode($response['body'], true);
            }
        }
        
        Logger::debug_log(
            sprintf('批量获取页面: %d/%d 成功', count($pages), count($page_ids)),
            'NotionApi'
        );
        
        return $pages;
    }
    
    /**
     * 搜索内容
     *
     * @param array $search_params 搜索参数
     * @return array 搜索结果
     */
    public function search(array $search_params): array {
        $response = $this->make_request('POST', '/search', $search_params);
        
        return $response['results'] ?? [];
    }
    
    /**
     * 发送API请求
     *
     * @param string $method HTTP方法
     * @param string $endpoint API端点
     * @param array $data 请求数据
     * @return array|null 响应数据
     */
    private function make_request(string $method, string $endpoint, array $data = []): ?array {
        $url = $this->base_url . $endpoint;
        
        $args = [
            'method' => $method,
            'headers' => $this->default_headers,
            'timeout' => 30,
            'data_format' => 'body'
        ];
        
        if (!empty($data)) {
            $args['body'] = json_encode($data);
        }
        
        // 使用重试机制
        $response = $this->retry->execute_with_retry(function() use ($url, $args) {
            return wp_remote_request($url, $args);
        });
        
        if (is_wp_error($response)) {
            Logger::debug_log(
                sprintf('API请求失败: %s - %s', $endpoint, $response->get_error_message()),
                'NotionApi'
            );
            return null;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        
        if ($status_code >= 200 && $status_code < 300) {
            return json_decode($body, true);
        }
        
        Logger::debug_log(
            sprintf('API请求错误: %s - 状态码 %d', $endpoint, $status_code),
            'NotionApi'
        );
        
        return null;
    }
    
    /**
     * 清理API缓存
     *
     * @param string|null $key 特定缓存键，null则清理所有
     */
    public function clear_cache(?string $key = null): void {
        if ($key) {
            $this->cache->delete($key, 'api_response');
        } else {
            $this->cache->flush_group('api_response');
        }
        
        Logger::debug_log('API缓存已清理', 'NotionApi');
    }
    
    /**
     * 获取API状态
     *
     * @return array API状态信息
     */
    public function get_api_status(): array {
        // 发送一个简单的请求测试API状态
        $response = $this->make_request('GET', '/users/me');
        
        return [
            'available' => $response !== null,
            'token_valid' => $response && !isset($response['code']),
            'user_info' => $response ?: null,
            'timestamp' => current_time('mysql')
        ];
    }
    
    // ========================================
    // 实现ApiInterface接口中缺失的方法
    // ========================================
    
    /**
     * 获取数据库页面 - 接口实现
     * 
     * @param string $database_id 数据库ID
     * @param array $filter 筛选条件
     * @return array 页面数据
     */
    public function get_database_pages(string $database_id, array $filter = []): array {
        return $this->get_database_pages_enhanced($database_id, $filter, false);
    }
    
    /**
     * 获取数据库页面增强版 (恢复原始功能)
     *
     * @param string $database_id 数据库ID
     * @param array $filter 筛选条件  
     * @param bool $with_details 是否获取详细信息
     * @return array 页面数据
     */
    public function get_database_pages_enhanced(string $database_id, array $filter = [], bool $with_details = false): array {
        Logger::debug_log(
            "获取数据库页面: {$database_id}, 详细信息: {$with_details}",
            'NotionApi'
        );
        
        $all_results = [];
        $has_more = true;
        $start_cursor = null;
        
        while ($has_more) {
            // 智能分页大小优化：根据数据量动态调整
            $options = get_option('notion_to_wordpress_options', []);
            $base_page_size = $options['api_page_size'] ?? 100;
            
            // 动态优化：首次请求使用较大分页，后续根据结果调整
            $page_size = empty($all_results) ? min(100, $base_page_size) : min(100, $base_page_size * 1.5);
            
            $data = [
                'page_size' => $page_size
            ];
            
            // 筛选条件验证和应用
            if ($this->is_valid_filter($filter)) {
                $data['filter'] = $filter;
            }
            
            if ($start_cursor) {
                $data['start_cursor'] = $start_cursor;
            }
            
            $response = $this->make_request('POST', "/databases/{$database_id}/query", $data);
            
            if (isset($response['results'])) {
                $all_results = array_merge($all_results, $response['results']);
                Logger::debug_log(
                    '获取数据库页面批次: ' . count($response['results']) . ', 总计: ' . count($all_results),
                    'NotionApi'
                );
            }
            
            $has_more = $response['has_more'] ?? false;
            $start_cursor = $response['next_cursor'] ?? null;
        }
        
        // 如果需要详细信息，批量获取页面详情
        if ($with_details && !empty($all_results)) {
            $all_results = $this->enrich_pages_with_details($all_results);
        }
        
        Logger::debug_log(
            sprintf('数据库页面获取完成: %d 页', count($all_results)),
            'NotionApi'
        );
        
        return $all_results;
    }
    
    /**
     * 获取单个页面 (接口实现)
     *
     * @param string $page_id 页面ID
     * @return array 页面数据
     */
    public function get_page(string $page_id): array {
        $result = $this->fetch_page($page_id);
        return $result ?: [];
    }
    
    /**
     * 获取页面内容 (接口实现)
     *
     * @param string $page_id 页面ID
     * @param int $depth 递归深度
     * @param int $max_depth 最大深度
     * @return array 页面内容
     */
    public function get_page_content(string $page_id, int $depth = 0, int $max_depth = 3): array {
        $cache_key = "page_content_{$page_id}_{$depth}_{$max_depth}";
        
        // 检查缓存
        $cached = $this->cache->get($cache_key, 'page_content');
        if ($cached !== null) {
            return $cached;
        }
        
        $blocks = $this->fetch_page_blocks($page_id);
        
        // 递归获取子块内容
        if ($depth < $max_depth) {
            foreach ($blocks as &$block) {
                if (isset($block['has_children']) && $block['has_children']) {
                    $block['children'] = $this->get_page_content($block['id'], $depth + 1, $max_depth);
                }
            }
        }
        
        // 缓存结果
        $this->cache->set($cache_key, $blocks, 300, 'page_content');
        
        return $blocks;
    }
    
    /**
     * 获取块的子内容 (接口实现)
     *
     * @param string $block_id 块ID
     * @return array 子内容
     */
    public function get_block_children(string $block_id): array {
        return $this->fetch_page_blocks($block_id);
    }
    
    /**
     * 获取数据库信息 (接口实现)
     *
     * @param string $database_id 数据库ID
     * @return array 数据库信息
     */
    public function get_database(string $database_id): array {
        $cache_key = "database_{$database_id}";
        
        // 检查缓存
        $cached = $this->cache->get($cache_key, 'database_structure');
        if ($cached !== null) {
            return $cached;
        }
        
        $response = $this->make_request('GET', "/databases/{$database_id}");
        
        if ($response) {
            $this->cache->set($cache_key, $response, 1800, 'database_structure');
        }
        
        return $response ?: [];
    }
    
    /**
     * 发送API请求 (接口实现)
     *
     * @param string $endpoint API端点
     * @param string $method HTTP方法
     * @param array $data 请求数据
     * @return array API响应
     */
    public function send_request(string $endpoint, string $method = 'GET', array $data = []): array {
        $result = $this->make_request($method, $endpoint, $data);
        return $result ?: [];
    }
    
    /**
     * 批量请求 (接口实现)
     *
     * @param array $requests 请求数组
     * @param array $options 选项
     * @return array 批量响应
     */
    public function batch_request(array $requests, array $options = []): array {
        if (empty($requests)) {
            return [];
        }
        
        // 构建并发请求
        $concurrent_requests = [];
        foreach ($requests as $key => $request) {
            $concurrent_requests[$key] = [
                'method' => $request['method'] ?? 'GET',
                'url' => $this->base_url . $request['endpoint'],
                'headers' => $this->default_headers,
                'body' => isset($request['data']) ? json_encode($request['data']) : null
            ];
        }
        
        $responses = $this->concurrency->execute_concurrent_requests($concurrent_requests);
        
        // 处理响应
        $results = [];
        foreach ($responses as $key => $response) {
            if ($response && isset($response['body'])) {
                $results[$key] = json_decode($response['body'], true) ?: [];
            } else {
                $results[$key] = [];
            }
        }
        
        return $results;
    }
    
    /**
     * 批量发送请求 (接口实现)
     *
     * @param array $endpoints 端点数组
     * @param string $method HTTP方法
     * @param array $data_array 数据数组
     * @param int $max_retries 最大重试次数
     * @param int $base_delay 基础延迟
     * @return array 响应数组
     */
    public function batch_send_requests(array $endpoints, string $method = 'GET', array $data_array = [], int $max_retries = 2, int $base_delay = 1000): array {
        $requests = [];
        
        foreach ($endpoints as $index => $endpoint) {
            $requests[$index] = [
                'endpoint' => $endpoint,
                'method' => $method,
                'data' => $data_array[$index] ?? []
            ];
        }
        
        return $this->batch_request($requests);
    }
    
    /**
     * 批量获取块子内容 (接口实现)
     *
     * @param array $block_ids 块ID数组
     * @return array 块内容数组
     */
    public function batch_get_block_children(array $block_ids): array {
        $requests = [];
        foreach ($block_ids as $block_id) {
            $requests[$block_id] = [
                'endpoint' => "/blocks/{$block_id}/children",
                'method' => 'GET'
            ];
        }
        
        return $this->batch_request($requests);
    }
    
    /**
     * 批量查询数据库 (接口实现)
     *
     * @param array $database_ids 数据库ID数组
     * @param array $filters 筛选条件数组
     * @return array 查询结果数组
     */
    public function batch_query_databases(array $database_ids, array $filters = []): array {
        $requests = [];
        
        foreach ($database_ids as $index => $database_id) {
            $requests[$index] = [
                'endpoint' => "/databases/{$database_id}/query",
                'method' => 'POST',
                'data' => $filters[$index] ?? []
            ];
        }
        
        return $this->batch_request($requests);
    }
    
    /**
     * 批量获取数据库信息 (接口实现)
     *
     * @param array $database_ids 数据库ID数组
     * @return array 数据库信息数组
     */
    public function batch_get_databases(array $database_ids): array {
        $requests = [];
        foreach ($database_ids as $database_id) {
            $requests[$database_id] = [
                'endpoint' => "/databases/{$database_id}",
                'method' => 'GET'
            ];
        }
        
        return $this->batch_request($requests);
    }
    
    // ========================================
    // 扩展方法：增强功能
    // ========================================
    
    /**
     * 批量获取页面 (从原API.php恢复)
     *
     * @param array $page_ids 页面ID数组
     * @return array 页面数据
     */
    public function batch_get_pages(array $page_ids): array {
        return $this->batch_fetch_pages($page_ids);
    }
    
    /**
     * 获取页面元数据
     *
     * @param string $page_id 页面ID
     * @return array 页面元数据
     */
    public function get_page_metadata(string $page_id): array {
        $page_data = $this->get_page($page_id);
        
        if (empty($page_data)) {
            return [];
        }
        
        return [
            'id' => $page_data['id'] ?? '',
            'created_time' => $page_data['created_time'] ?? '',
            'last_edited_time' => $page_data['last_edited_time'] ?? '',
            'created_by' => $page_data['created_by'] ?? [],
            'last_edited_by' => $page_data['last_edited_by'] ?? [],
            'cover' => $page_data['cover'] ?? null,
            'icon' => $page_data['icon'] ?? null,
            'parent' => $page_data['parent'] ?? [],
            'archived' => $page_data['archived'] ?? false,
            'properties' => $page_data['properties'] ?? []
        ];
    }
    
    /**
     * 获取数据库信息（扩展版本）
     *
     * @param string $database_id 数据库ID
     * @return array 数据库信息
     */
    public function get_database_info(string $database_id): array {
        return $this->get_database($database_id);
    }
    
    /**
     * 获取页面详情
     *
     * @param string $page_id 页面ID
     * @return array 页面详情
     */
    public function get_page_details(string $page_id): array {
        $page_data = $this->get_page($page_id);
        if (empty($page_data)) {
            return [];
        }
        
        // 获取页面内容
        $content = $this->get_page_content($page_id);
        
        return array_merge($page_data, [
            'content_blocks' => $content,
            'block_count' => count($content)
        ]);
    }
    
    /**
     * 检查API密钥是否已设置
     *
     * @return bool API密钥是否已设置
     */
    public function is_api_key_set(): bool {
        return !empty($this->api_token);
    }
    
    /**
     * 检查连接状态
     *
     * @return bool 连接是否正常
     */
    public function check_connection(): bool {
        $status = $this->get_api_status();
        return $status['available'] && $status['token_valid'];
    }
    
    /**
     * 测试连接（扩展版本）
     *
     * @param string $database_id 可选的数据库ID用于测试
     * @return array 连接测试结果
     */
    public function test_connection(string $database_id = ''): array {
        $status = $this->get_api_status();
        
        $result = [
            'api_accessible' => $status['available'],
            'token_valid' => $status['token_valid'],
            'user_info' => $status['user_info'],
            'database_accessible' => false,
            'database_info' => null
        ];
        
        // 如果提供了数据库ID，测试数据库访问
        if (!empty($database_id) && $status['available'] && $status['token_valid']) {
            $db_info = $this->get_database($database_id);
            $result['database_accessible'] = !empty($db_info);
            $result['database_info'] = $db_info;
        }
        
        return $result;
    }
    
    // ========================================
    // 关键辅助方法 (从原始API.php恢复)
    // ========================================
    
    /**
     * 筛选条件验证 (从原始86行实现恢复)
     *
     * @param array $filter 筛选条件
     * @return bool 是否有效
     */
    private function is_valid_filter(array $filter): bool {
        // 记录过滤器验证开始
        Logger::debug_log(
            sprintf('开始验证过滤器: %s', json_encode($filter, JSON_UNESCAPED_UNICODE)),
            'NotionApi'
        );
        
        // 如果过滤器为空，返回false
        if (empty($filter)) {
            Logger::debug_log('无过滤器：将获取所有页面（首次同步或全量获取）', 'NotionApi');
            return false;
        }
        
        // 检查是否包含有效的过滤条件
        $valid_filter_keys = [
            'and', 'or', 'title', 'rich_text', 'number', 'checkbox', 'select',
            'multi_select', 'status', 'date', 'people', 'files', 'url', 'email',
            'phone_number', 'relation', 'created_by', 'created_time',
            'last_edited_by', 'last_edited_time', 'formula', 'unique_id', 'rollup',
            'timestamp', 'property'  // 添加时间戳过滤器和属性过滤器支持
        ];
        
        // 递归检查过滤器结构
        return $this->validate_filter_structure($filter, $valid_filter_keys);
    }
    
    /**
     * 递归验证过滤器结构
     *
     * @param array $filter 过滤器
     * @param array $valid_keys 有效键名
     * @return bool 是否有效
     */
    private function validate_filter_structure(array $filter, array $valid_keys): bool {
        foreach ($filter as $key => $value) {
            if (!in_array($key, $valid_keys)) {
                Logger::debug_log("无效的过滤器键: {$key}", 'NotionApi');
                return false;
            }
            
            // 如果值是数组，递归验证
            if (is_array($value) && in_array($key, ['and', 'or'])) {
                foreach ($value as $sub_filter) {
                    if (is_array($sub_filter) && !$this->validate_filter_structure($sub_filter, $valid_keys)) {
                        return false;
                    }
                }
            }
        }
        
        return true;
    }
    
    /**
     * 页面详情增强 (从原始实现恢复)
     *
     * @param array $pages 页面数组
     * @return array 增强后的页面数组
     */
    private function enrich_pages_with_details(array $pages): array {
        // 对于大量页面，跳过详细信息获取以提高性能
        if (count($pages) > 20) {
            Logger::debug_log(
                '页面数量过多(' . count($pages) . ')，跳过详细信息获取以提高性能',
                'NotionApi'
            );
            return $pages;
        }
        
        $enriched_pages = [];
        $failed_count = 0;
        $max_failures = 5; // 最多允许5次失败
        
        foreach ($pages as $page) {
            $page_id = $page['id'] ?? '';
            if (empty($page_id)) {
                $enriched_pages[] = $page;
                continue;
            }
            
            // 如果失败次数过多，跳过剩余页面的详细信息获取
            if ($failed_count >= $max_failures) {
                $enriched_pages[] = $page;
                continue;
            }
            
            try {
                // 获取页面详细信息
                $page_details = $this->get_page_details($page_id);
                
                if (!empty($page_details)) {
                    // 合并基本信息和详细信息
                    $enriched_page = array_merge($page, [
                        'cover' => $page_details['cover'] ?? null,
                        'icon' => $page_details['icon'] ?? null,
                        'url' => $page_details['url'] ?? $page['url'] ?? null,
                    ]);
                    $enriched_pages[] = $enriched_page;
                } else {
                    $enriched_pages[] = $page;
                    $failed_count++;
                }
            } catch (\Exception $e) {
                // 快速跳过失败的页面，不记录详细日志
                $enriched_pages[] = $page;
                $failed_count++;
            }
        }
        
        return $enriched_pages;
    }
}
